/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
// biome-ignore lint: disable
export {}
declare global {
  const avatarGroupInjectionKey: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/useAvatarGroup.js')['avatarGroupInjectionKey']
  const buttonGroupInjectionKey: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/useButtonGroup.js')['buttonGroupInjectionKey']
  const defineLocale: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/defineLocale.js')['defineLocale']
  const defineShortcuts: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/defineShortcuts.js')['defineShortcuts']
  const extendLocale: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/defineLocale.js')['extendLocale']
  const extractShortcuts: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/defineShortcuts.js')['extractShortcuts']
  const formBusInjectionKey: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/useFormField.js')['formBusInjectionKey']
  const formFieldInjectionKey: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/useFormField.js')['formFieldInjectionKey']
  const formInputsInjectionKey: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/useFormField.js')['formInputsInjectionKey']
  const formLoadingInjectionKey: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/useFormField.js')['formLoadingInjectionKey']
  const formOptionsInjectionKey: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/useFormField.js')['formOptionsInjectionKey']
  const inputIdInjectionKey: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/useFormField.js')['inputIdInjectionKey']
  const kbdKeysMap: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/useKbd.js')['kbdKeysMap']
  const localeContextInjectionKey: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/useLocale.js')['localeContextInjectionKey']
  const portalTargetInjectionKey: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/usePortal.js')['portalTargetInjectionKey']
  const useAppConfig: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/vue/composables/useAppConfig.js')['useAppConfig']
  const useAvatarGroup: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/useAvatarGroup.js')['useAvatarGroup']
  const useButtonGroup: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/useButtonGroup.js')['useButtonGroup']
  const useComponentIcons: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/useComponentIcons.js')['useComponentIcons']
  const useFileUpload: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/useFileUpload.js')['useFileUpload']
  const useFormField: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/useFormField.js')['useFormField']
  const useKbd: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/useKbd.js')['useKbd']
  const useLocale: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/useLocale.js')['useLocale']
  const useOverlay: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/useOverlay.js')['useOverlay']
  const usePortal: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/usePortal.js')['usePortal']
  const useToast: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/useToast.js')['useToast']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { ShortcutConfig, ShortcutsConfig, ShortcutsOptions } from './node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/defineShortcuts.d'
  import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/defineShortcuts.d')
  // @ts-ignore
  export type { UseComponentIconsProps } from './node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/useComponentIcons.d'
  import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/useComponentIcons.d')
  // @ts-ignore
  export type { UseFileUploadOptions } from './node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/useFileUpload.d'
  import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/useFileUpload.d')
  // @ts-ignore
  export type { KbdKey, KbdKeySpecific } from './node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/useKbd.d'
  import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/useKbd.d')
  // @ts-ignore
  export type { OverlayOptions, Overlay } from './node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/useOverlay.d'
  import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/useOverlay.d')
  // @ts-ignore
  export type { Toast } from './node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/useToast.d'
  import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/composables/useToast.d')
}
