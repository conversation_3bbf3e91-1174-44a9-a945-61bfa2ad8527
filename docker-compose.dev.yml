version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: admin_mysql_dev
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: admin_system
      MYSQL_USER: admin
      MYSQL_PASSWORD: admin123
    ports:
      - "3306:3306"
    volumes:
      - mysql_dev_data:/var/lib/mysql
      - ./docker/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - admin_dev_network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: admin_app_dev
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - ADMIN_DATABASE_HOST=mysql
      - ADMIN_DATABASE_PORT=3306
      - ADMIN_DATABASE_USER=root
      - ADMIN_DATABASE_PASSWORD=password
      - ADMIN_DATABASE_DBNAME=admin_system
      - ADMIN_JWT_SECRET=your-super-secret-jwt-key-change-in-production
      - ADMIN_SERVER_HOST=0.0.0.0
      - ADMIN_SERVER_PORT=8080
    volumes:
      - .:/app
      - /app/tmp
      - /app/web/node_modules
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - admin_dev_network

volumes:
  mysql_dev_data:

networks:
  admin_dev_network:
    driver: bridge
