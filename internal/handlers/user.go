package handlers

import (
	"net/http"
	"strconv"

	"github.com/labstack/echo/v4"
	"xiaoxingcloud.com/admin/internal/services"
)

type UserHandler struct {
	userService *services.UserService
	rbacService *services.RBACService
}

type CreateUserRequest struct {
	Username  string `json:"username" validate:"required,min=3,max=50"`
	Email     string `json:"email" validate:"required,email"`
	Password  string `json:"password" validate:"required,min=8"`
	FirstName string `json:"first_name" validate:"required"`
	LastName  string `json:"last_name" validate:"required"`
}

type UpdateUserRequest struct {
	Username  string `json:"username,omitempty" validate:"omitempty,min=3,max=50"`
	Email     string `json:"email,omitempty" validate:"omitempty,email"`
	FirstName string `json:"first_name,omitempty"`
	LastName  string `json:"last_name,omitempty"`
	IsActive  *bool  `json:"is_active,omitempty"`
}

type AssignRoleRequest struct {
	RoleID uint `json:"role_id" validate:"required"`
}

func NewUserHandler(userService *services.UserService, rbacService *services.RBACService) *UserHandler {
	return &UserHandler{
		userService: userService,
		rbacService: rbacService,
	}
}

// GetUsers returns all users with pagination
func (h *UserHandler) GetUsers(c echo.Context) error {
	page, _ := strconv.Atoi(c.QueryParam("page"))
	if page < 1 {
		page = 1
	}

	limit, _ := strconv.Atoi(c.QueryParam("limit"))
	if limit < 1 || limit > 100 {
		limit = 10
	}

	users, total, err := h.userService.GetAllUsers(page, limit)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	// Remove passwords from response
	var userResponses []map[string]any
	for _, user := range users {
		userResponse := map[string]any{
			"id":         user.ID,
			"username":   user.Username,
			"email":      user.Email,
			"first_name": user.FirstName,
			"last_name":  user.LastName,
			"is_active":  user.IsActive,
			"roles":      user.Roles,
			"created_at": user.CreatedAt,
			"updated_at": user.UpdatedAt,
		}
		userResponses = append(userResponses, userResponse)
	}

	return c.JSON(http.StatusOK, map[string]any{
		"users": userResponses,
		"pagination": map[string]any{
			"page":  page,
			"limit": limit,
			"total": total,
		},
	})
}

// GetUser returns a specific user by ID
func (h *UserHandler) GetUser(c echo.Context) error {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid user ID")
	}

	user, err := h.userService.GetUserByID(uint(id))
	if err != nil {
		return echo.NewHTTPError(http.StatusNotFound, err.Error())
	}

	// Return user without password
	userResponse := map[string]any{
		"id":         user.ID,
		"username":   user.Username,
		"email":      user.Email,
		"first_name": user.FirstName,
		"last_name":  user.LastName,
		"is_active":  user.IsActive,
		"roles":      user.Roles,
		"attributes": user.Attributes,
		"created_at": user.CreatedAt,
		"updated_at": user.UpdatedAt,
	}

	return c.JSON(http.StatusOK, userResponse)
}

// CreateUser creates a new user
func (h *UserHandler) CreateUser(c echo.Context) error {
	var req CreateUserRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid request body")
	}

	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	user, err := h.userService.CreateUser(req.Username, req.Email, req.Password, req.FirstName, req.LastName)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// Return user without password
	userResponse := map[string]any{
		"id":         user.ID,
		"username":   user.Username,
		"email":      user.Email,
		"first_name": user.FirstName,
		"last_name":  user.LastName,
		"is_active":  user.IsActive,
		"roles":      user.Roles,
		"created_at": user.CreatedAt,
		"updated_at": user.UpdatedAt,
	}

	return c.JSON(http.StatusCreated, userResponse)
}

// UpdateUser updates a user
func (h *UserHandler) UpdateUser(c echo.Context) error {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid user ID")
	}

	var req UpdateUserRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid request body")
	}

	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// Convert request to map for updates
	updates := make(map[string]any)
	if req.Username != "" {
		updates["username"] = req.Username
	}
	if req.Email != "" {
		updates["email"] = req.Email
	}
	if req.FirstName != "" {
		updates["first_name"] = req.FirstName
	}
	if req.LastName != "" {
		updates["last_name"] = req.LastName
	}
	if req.IsActive != nil {
		// Check if super admin is trying to deactivate themselves
		currentUserID, ok := c.Get("user_id").(uint)
		if ok && currentUserID == uint(id) && !*req.IsActive {
			// Get current user to check if they are super admin
			currentUser, err := h.userService.GetUserByID(currentUserID)
			if err == nil && currentUser.Username == "admin" {
				return echo.NewHTTPError(http.StatusForbidden, "super administrator cannot deactivate themselves")
			}
		}
		updates["is_active"] = *req.IsActive
	}

	user, err := h.userService.UpdateUser(uint(id), updates)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// Return user without password
	userResponse := map[string]any{
		"id":         user.ID,
		"username":   user.Username,
		"email":      user.Email,
		"first_name": user.FirstName,
		"last_name":  user.LastName,
		"is_active":  user.IsActive,
		"updated_at": user.UpdatedAt,
	}

	return c.JSON(http.StatusOK, userResponse)
}

// DeleteUser deletes a user
func (h *UserHandler) DeleteUser(c echo.Context) error {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid user ID")
	}

	// Get current user ID from JWT token
	currentUserID, ok := c.Get("user_id").(uint)
	if !ok {
		return echo.NewHTTPError(http.StatusUnauthorized, "user not authenticated")
	}

	// Check if user is trying to delete themselves
	targetUserID := uint(id)
	if currentUserID == targetUserID {
		// Get current user to check if they are super admin
		currentUser, err := h.userService.GetUserByID(currentUserID)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, "failed to get current user")
		}

		// Check if current user is super admin (username "admin")
		if currentUser.Username == "admin" {
			return echo.NewHTTPError(http.StatusForbidden, "super administrator cannot delete themselves")
		}
	}

	if err := h.userService.DeleteUser(targetUserID); err != nil {
		return echo.NewHTTPError(http.StatusNotFound, err.Error())
	}

	return c.JSON(http.StatusOK, map[string]string{
		"message": "user deleted successfully",
	})
}

// AssignRole assigns a role to a user
func (h *UserHandler) AssignRole(c echo.Context) error {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid user ID")
	}

	var req AssignRoleRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid request body")
	}

	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	if err := h.rbacService.AssignRoleToUser(uint(userID), req.RoleID); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	return c.JSON(http.StatusOK, map[string]string{
		"message": "role assigned successfully",
	})
}

// RemoveRole removes a role from a user
func (h *UserHandler) RemoveRole(c echo.Context) error {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid user ID")
	}

	roleID, err := strconv.ParseUint(c.Param("roleId"), 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid role ID")
	}

	if err := h.rbacService.RemoveRoleFromUser(uint(userID), uint(roleID)); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	return c.JSON(http.StatusOK, map[string]string{
		"message": "role removed successfully",
	})
}
