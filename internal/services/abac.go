package services

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"
	"xiaoxingcloud.com/admin/internal/models"
)

type ABACService struct {
	db *gorm.DB
}

type PolicyCondition struct {
	Attribute string `json:"attribute"`
	Operator  string `json:"operator"` // eq, ne, gt, lt, gte, lte, in, contains
	Value     any    `json:"value"`
}

type PolicyRule struct {
	Conditions []PolicyCondition `json:"conditions"`
	Logic      string            `json:"logic"` // and, or
}

func NewABACService(db *gorm.DB) *ABACService {
	return &ABACService{db: db}
}

// CheckAccess evaluates ABAC policies for a user's access to a resource
func (a *ABACService) CheckAccess(userID uint, resource, action string, context map[string]any) (bool, error) {
	// Get user attributes
	userAttributes, err := a.GetUserAttributes(userID)
	if err != nil {
		return false, fmt.Errorf("failed to get user attributes: %w", err)
	}

	// Convert user attributes to map for easier access
	attributeMap := make(map[string]any)
	for _, attr := range userAttributes {
		attributeMap[attr.Name] = a.convertAttributeValue(attr.Value, attr.Type)
	}

	// Add context attributes
	for key, value := range context {
		attributeMap[key] = value
	}

	// Get applicable policies
	policies, err := a.GetPolicies(resource, action)
	if err != nil {
		return false, fmt.Errorf("failed to get policies: %w", err)
	}

	// Evaluate policies
	for _, policy := range policies {
		if !policy.IsActive {
			continue
		}

		allowed, err := a.evaluatePolicy(policy, attributeMap)
		if err != nil {
			continue // Skip invalid policies
		}

		if policy.Effect == "deny" && allowed {
			return false, nil // Explicit deny
		}

		if policy.Effect == "allow" && allowed {
			return true, nil // Explicit allow
		}
	}

	return false, nil // Default deny
}

// GetUserAttributes returns all attributes for a user
func (a *ABACService) GetUserAttributes(userID uint) ([]models.Attribute, error) {
	var attributes []models.Attribute
	err := a.db.Where("user_id = ?", userID).Find(&attributes).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get user attributes: %w", err)
	}

	return attributes, nil
}

// SetUserAttribute sets an attribute for a user
func (a *ABACService) SetUserAttribute(userID uint, name, value, attrType string) error {
	var attribute models.Attribute
	err := a.db.Where("user_id = ? AND name = ?", userID, name).First(&attribute).Error

	if err == gorm.ErrRecordNotFound {
		// Create new attribute
		attribute = models.Attribute{
			UserID: userID,
			Name:   name,
			Value:  value,
			Type:   attrType,
		}
		return a.db.Create(&attribute).Error
	} else if err != nil {
		return fmt.Errorf("failed to check existing attribute: %w", err)
	}

	// Update existing attribute
	attribute.Value = value
	attribute.Type = attrType
	return a.db.Save(&attribute).Error
}

// GetPolicies returns policies for a resource and action
func (a *ABACService) GetPolicies(resource, action string) ([]models.Policy, error) {
	var policies []models.Policy
	err := a.db.Where("resource = ? AND action = ? AND is_active = ?", resource, action, true).Find(&policies).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get policies: %w", err)
	}

	return policies, nil
}

// CreatePolicy creates a new ABAC policy
func (a *ABACService) CreatePolicy(name, description, resource, action, effect string, conditions PolicyRule) error {
	conditionsJSON, err := json.Marshal(conditions)
	if err != nil {
		return fmt.Errorf("failed to marshal conditions: %w", err)
	}

	policy := models.Policy{
		Name:        name,
		Description: description,
		Resource:    resource,
		Action:      action,
		Effect:      effect,
		Conditions:  string(conditionsJSON),
		IsActive:    true,
	}

	return a.db.Create(&policy).Error
}

// evaluatePolicy evaluates a single policy against user attributes
func (a *ABACService) evaluatePolicy(policy models.Policy, attributes map[string]any) (bool, error) {
	var rule PolicyRule
	if err := json.Unmarshal([]byte(policy.Conditions), &rule); err != nil {
		return false, fmt.Errorf("failed to unmarshal policy conditions: %w", err)
	}

	results := make([]bool, len(rule.Conditions))

	for i, condition := range rule.Conditions {
		result, err := a.evaluateCondition(condition, attributes)
		if err != nil {
			return false, err
		}
		results[i] = result
	}

	// Apply logic (AND/OR)
	if rule.Logic == "or" {
		for _, result := range results {
			if result {
				return true, nil
			}
		}
		return false, nil
	} else { // Default to AND
		for _, result := range results {
			if !result {
				return false, nil
			}
		}
		return true, nil
	}
}

// evaluateCondition evaluates a single condition
func (a *ABACService) evaluateCondition(condition PolicyCondition, attributes map[string]any) (bool, error) {
	attrValue, exists := attributes[condition.Attribute]
	if !exists {
		return false, nil
	}

	switch condition.Operator {
	case "eq":
		return a.compareValues(attrValue, condition.Value, "eq"), nil
	case "ne":
		return !a.compareValues(attrValue, condition.Value, "eq"), nil
	case "gt":
		return a.compareValues(attrValue, condition.Value, "gt"), nil
	case "lt":
		return a.compareValues(attrValue, condition.Value, "lt"), nil
	case "gte":
		return a.compareValues(attrValue, condition.Value, "gte"), nil
	case "lte":
		return a.compareValues(attrValue, condition.Value, "lte"), nil
	case "in":
		return a.checkInArray(attrValue, condition.Value), nil
	case "contains":
		return a.checkContains(attrValue, condition.Value), nil
	default:
		return false, fmt.Errorf("unknown operator: %s", condition.Operator)
	}
}

// Helper functions for value comparison and conversion
func (a *ABACService) convertAttributeValue(value, attrType string) any {
	switch attrType {
	case "number":
		if num, err := strconv.ParseFloat(value, 64); err == nil {
			return num
		}
	case "boolean":
		if b, err := strconv.ParseBool(value); err == nil {
			return b
		}
	case "date":
		if t, err := time.Parse(time.RFC3339, value); err == nil {
			return t
		}
	}
	return value // Default to string
}

func (a *ABACService) compareValues(a1, a2 any, op string) bool {
	// Implementation of value comparison logic
	// This is a simplified version - in production, you'd want more robust type handling
	switch op {
	case "eq":
		return fmt.Sprintf("%v", a1) == fmt.Sprintf("%v", a2)
	case "gt", "lt", "gte", "lte":
		// Numeric comparison logic would go here
		return false
	}
	return false
}

func (a *ABACService) checkInArray(value any, array any) bool {
	// Check if value is in array
	return false // Simplified implementation
}

func (a *ABACService) checkContains(value any, substring any) bool {
	return strings.Contains(fmt.Sprintf("%v", value), fmt.Sprintf("%v", substring))
}
