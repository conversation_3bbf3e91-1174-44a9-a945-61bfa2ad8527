package services

import (
	"errors"
	"fmt"

	"gorm.io/gorm"
	"xiaoxingcloud.com/admin/internal/models"
)

type RBACService struct {
	db *gorm.DB
}

func NewRBACService(db *gorm.DB) *RBACService {
	return &RBACService{db: db}
}

// CheckPermission checks if a user has a specific permission
func (r *RBACService) CheckPermission(userID uint, resource, action string) (bool, error) {
	var count int64

	// Query to check if user has the permission through any of their roles
	err := r.db.Table("users").
		Joins("JOIN user_roles ON users.id = user_roles.user_id").
		Joins("JOIN roles ON user_roles.role_id = roles.id").
		Joins("JOIN role_permissions ON roles.id = role_permissions.role_id").
		Joins("JOIN permissions ON role_permissions.permission_id = permissions.id").
		Where("users.id = ? AND users.is_active = ? AND roles.is_active = ?", userID, true, true).
		Where("permissions.resource = ? AND permissions.action = ?", resource, action).
		Count(&count).Error

	if err != nil {
		return false, fmt.Errorf("failed to check permission: %w", err)
	}

	return count > 0, nil
}

// GetUserPermissions returns all permissions for a user
func (r *RBACService) GetUserPermissions(userID uint) ([]models.Permission, error) {
	var permissions []models.Permission

	err := r.db.Table("permissions").
		Select("DISTINCT permissions.*").
		Joins("JOIN role_permissions ON permissions.id = role_permissions.permission_id").
		Joins("JOIN roles ON role_permissions.role_id = roles.id").
		Joins("JOIN user_roles ON roles.id = user_roles.role_id").
		Joins("JOIN users ON user_roles.user_id = users.id").
		Where("users.id = ? AND users.is_active = ? AND roles.is_active = ?", userID, true, true).
		Find(&permissions).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get user permissions: %w", err)
	}

	return permissions, nil
}

// GetUserRoles returns all roles for a user
func (r *RBACService) GetUserRoles(userID uint) ([]models.Role, error) {
	var user models.User
	err := r.db.Preload("Roles").Where("id = ?", userID).First(&user).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get user roles: %w", err)
	}

	return user.Roles, nil
}

// AssignRoleToUser assigns a role to a user
func (r *RBACService) AssignRoleToUser(userID, roleID uint) error {
	// Check if user exists
	var user models.User
	if err := r.db.First(&user, userID).Error; err != nil {
		return fmt.Errorf("user not found: %w", err)
	}

	// Check if role exists
	var role models.Role
	if err := r.db.First(&role, roleID).Error; err != nil {
		return fmt.Errorf("role not found: %w", err)
	}

	// Check if assignment already exists
	var userRole models.UserRole
	err := r.db.Where("user_id = ? AND role_id = ?", userID, roleID).First(&userRole).Error
	if err == nil {
		return errors.New("user already has this role")
	}
	if err != gorm.ErrRecordNotFound {
		return fmt.Errorf("failed to check existing role assignment: %w", err)
	}

	// Create the assignment
	userRole = models.UserRole{
		UserID: userID,
		RoleID: roleID,
	}

	if err := r.db.Create(&userRole).Error; err != nil {
		return fmt.Errorf("failed to assign role to user: %w", err)
	}

	return nil
}

// RemoveRoleFromUser removes a role from a user
func (r *RBACService) RemoveRoleFromUser(userID, roleID uint) error {
	result := r.db.Where("user_id = ? AND role_id = ?", userID, roleID).Delete(&models.UserRole{})
	if result.Error != nil {
		return fmt.Errorf("failed to remove role from user: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return errors.New("role assignment not found")
	}

	return nil
}

// AssignPermissionToRole assigns a permission to a role
func (r *RBACService) AssignPermissionToRole(roleID, permissionID uint) error {
	// Check if role exists
	var role models.Role
	if err := r.db.First(&role, roleID).Error; err != nil {
		return fmt.Errorf("role not found: %w", err)
	}

	// Check if permission exists
	var permission models.Permission
	if err := r.db.First(&permission, permissionID).Error; err != nil {
		return fmt.Errorf("permission not found: %w", err)
	}

	// Check if assignment already exists
	var rolePermission models.RolePermission
	err := r.db.Where("role_id = ? AND permission_id = ?", roleID, permissionID).First(&rolePermission).Error
	if err == nil {
		return errors.New("role already has this permission")
	}
	if err != gorm.ErrRecordNotFound {
		return fmt.Errorf("failed to check existing permission assignment: %w", err)
	}

	// Create the assignment
	rolePermission = models.RolePermission{
		RoleID:       roleID,
		PermissionID: permissionID,
	}

	if err := r.db.Create(&rolePermission).Error; err != nil {
		return fmt.Errorf("failed to assign permission to role: %w", err)
	}

	return nil
}

// RemovePermissionFromRole removes a permission from a role
func (r *RBACService) RemovePermissionFromRole(roleID, permissionID uint) error {
	result := r.db.Where("role_id = ? AND permission_id = ?", roleID, permissionID).Delete(&models.RolePermission{})
	if result.Error != nil {
		return fmt.Errorf("failed to remove permission from role: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return errors.New("permission assignment not found")
	}

	return nil
}
