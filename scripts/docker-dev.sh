#!/bin/bash

# Docker development script with hot reloading

echo "🐳 Starting Docker development environment with hot reloading..."

# Stop any existing containers
echo "🛑 Stopping existing containers..."
docker-compose -f docker-compose.dev.yml down

# Build and start development containers
echo "🏗️  Building and starting development containers..."
docker-compose -f docker-compose.dev.yml up --build

echo "✅ Development environment is ready!"
echo "📝 Backend API: http://localhost:8080"
echo "🔍 Health check: http://localhost:8080/health"
