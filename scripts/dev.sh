#!/bin/bash

# Development script for hot reloading

echo "🚀 Starting development environment with hot reloading..."

# Check if Air is installed
if ! command -v air &> /dev/null; then
    echo "📦 Installing Air..."
    go install github.com/air-verse/air@latest
fi

# Build frontend first
echo "🏗️  Building frontend..."
cd web
npm ci
npm run build
cd ..

# Start Air for hot reloading
echo "🔥 Starting Air hot reloading..."
air -c .air.toml
