# Development Dockerfile with Air for hot reloading
FROM golang:1.25-alpine

# Install necessary packages
RUN apk add --no-cache git curl

# Install Air for hot reloading
RUN go install github.com/air-verse/air@latest

# Install Node.js for frontend development
RUN apk add --no-cache nodejs npm

WORKDIR /app

# Copy go mod files first for better caching
COPY go.mod go.sum ./
RUN go mod download

# Copy the entire project
COPY . .

# Build frontend initially
WORKDIR /app/web
RUN npm ci && npm run build

# Go back to app root
WORKDIR /app

# Create tmp directory for Air
RUN mkdir -p tmp

# Expose port
EXPOSE 8080

# Use Air for hot reloading
CMD ["air", "-c", ".air.toml"]
