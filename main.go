package main

import (
	"embed"
	"io/fs"
	"log"
	"net/http"

	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"

	"xiaoxingcloud.com/admin/internal/auth"
	"xiaoxingcloud.com/admin/internal/config"
	"xiaoxingcloud.com/admin/internal/database"
	"xiaoxingcloud.com/admin/internal/handlers"
	authMiddleware "xiaoxingcloud.com/admin/internal/middleware"
	"xiaoxingcloud.com/admin/internal/services"
	"xiaoxingcloud.com/admin/pkg"
)

//go:embed web/dist
var frontendFS embed.FS

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatal("Failed to load configuration:", err)
	}

	// Validate configuration
	if err := cfg.Validate(); err != nil {
		log.Fatal("Configuration validation failed:", err)
	}

	// Print configuration
	cfg.PrintConfig()

	// Connect to database
	if err := database.Connect(cfg); err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// Run migrations
	if err := database.Migrate(); err != nil {
		log.Fatal("Failed to run migrations:", err)
	}

	// Seed database
	if err := database.Seed(); err != nil {
		log.Fatal("Failed to seed database:", err)
	}

	// Initialize services
	db := database.GetDB()
	userService := services.NewUserService(db)
	rbacService := services.NewRBACService(db)
	abacService := services.NewABACService(db)
	jwtService := auth.NewJWTService(cfg)

	// Initialize handlers
	authHandler := handlers.NewAuthHandler(userService, jwtService)
	userHandler := handlers.NewUserHandler(userService, rbacService)

	// Initialize middleware
	authMW := authMiddleware.NewAuthMiddleware(jwtService, rbacService, abacService)

	// Initialize Echo
	e := echo.New()

	// Set validator
	e.Validator = pkg.NewValidator()

	// Middleware
	e.Use(middleware.Logger())
	e.Use(middleware.Recover())
	e.Use(middleware.CORS())

	// Serve embedded frontend
	frontendSubFS, err := fs.Sub(frontendFS, "web/dist")
	if err != nil {
		log.Fatal("Failed to create frontend sub filesystem:", err)
	}
	e.Use(middleware.StaticWithConfig(middleware.StaticConfig{
		Root:       "/",
		Filesystem: http.FS(frontendSubFS),
		HTML5:      true,
	}))

	// API routes
	api := e.Group("/api/v1")

	// Public routes
	auth := api.Group("/auth")
	auth.POST("/login", authHandler.Login)
	auth.POST("/register", authHandler.Register)
	auth.POST("/refresh", authHandler.RefreshToken)

	// Protected routes
	protected := api.Group("")
	protected.Use(authMW.JWTAuth())

	// Auth protected routes
	protected.GET("/auth/profile", authHandler.GetProfile)
	protected.POST("/auth/logout", authHandler.Logout)

	// User management routes (require admin permissions)
	users := protected.Group("/users")
	users.Use(authMW.RequirePermission("users", "read"))
	users.GET("", userHandler.GetUsers)
	users.GET("/:id", userHandler.GetUser)

	// User creation/modification (require admin permissions)
	users.POST("", userHandler.CreateUser, authMW.RequirePermission("users", "create"))
	users.PUT("/:id", userHandler.UpdateUser, authMW.RequirePermission("users", "update"))
	users.DELETE("/:id", userHandler.DeleteUser, authMW.RequirePermission("users", "delete"))

	// Role assignment (require admin permissions)
	users.POST("/:id/roles", userHandler.AssignRole, authMW.RequirePermission("roles", "update"))
	users.DELETE("/:id/roles/:roleId", userHandler.RemoveRole, authMW.RequirePermission("roles", "update"))

	// Health check
	e.GET("/health", func(c echo.Context) error {
		return c.JSON(http.StatusOK, map[string]string{
			"status": "healthy",
		})
	})

	// Start server
	log.Printf("Server starting on %s:%s", cfg.Server.Host, cfg.Server.Port)
	log.Fatal(e.Start(cfg.Server.Host + ":" + cfg.Server.Port))
}
